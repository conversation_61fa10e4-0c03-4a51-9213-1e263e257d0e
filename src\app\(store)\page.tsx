import { publicUrl } from "@/env.mjs";
import { getTranslations } from "@/i18n/server";
import { safeProductBrowse } from "@/lib/commerce-utils";
import StoreConfig from "@/store.config";
import { CategoryBox } from "@/ui/category-box";
import { CategoryFilter } from "@/ui/category-filter";
import { ProductList } from "@/ui/products/product-list";
import { Button } from "@/ui/shadcn/button";
import { YnsLink } from "@/ui/yns-link";
import Image from "next/image";
import type { Metadata } from "next/types";

export const metadata = {
	title: "eClair Marketplace - Quality Lifestyle Goods",
	description:
		"Discover quality lifestyle goods at eClair Marketplace. Shop household items, kitchenware, clothing, accessories, and swimwear from our home-based business in Surrey, BC, Canada.",
	alternates: { canonical: publicUrl },
} satisfies Metadata;

export default async function Home() {
	const products = await safeProductBrowse({ first: 6 });
	const t = await getTranslations("/");

	return (
		<main>
			<section className="bg-background py-16 sm:py-24">
				<div className="mx-auto grid grid-cols-1 items-center justify-items-center gap-12 px-8 sm:px-16 md:grid-cols-2 max-w-7xl">
					<div className="max-w-lg space-y-8">
						<h2 className="text-balance text-4xl text-display text-foreground md:text-5xl lg:text-6xl">
							{t("hero.title")}
						</h2>
						<p className="text-pretty text-xl text-elegant text-muted-foreground leading-relaxed">
							{t("hero.description")}
						</p>
						<Button asChild size="lg" className="rounded-lg shadow-card">
							<YnsLink href={t("hero.link")}>{t("hero.action")}</YnsLink>
						</Button>
					</div>
					<div className="relative">
						<div className="absolute inset-0 bg-gradient-to-br from-muted to-background rounded-2xl transform rotate-3"></div>
						<Image
							alt="Featured Product"
							loading="eager"
							priority={true}
							className="relative rounded-2xl shadow-card border border-elegant"
							height={500}
							width={500}
							src="https://files.stripe.com/links/MDB8YWNjdF8xT3BaeG5GSmNWbVh6bURsfGZsX3Rlc3RfaDVvWXowdU9ZbWlobUIyaHpNc1hCeDM200NBzvUjqP"
							style={{
								objectFit: "cover",
							}}
							sizes="(max-width: 640px) 90vw, 500px"
						/>
					</div>
				</div>
			</section>

			{/* Products Section with Category Filter */}
			<section className="w-full py-16 bg-muted/30">
				<div className="max-w-7xl mx-auto px-8 sm:px-16">
					<div className="mb-12 text-center">
						<h2 className="text-3xl text-display text-foreground mb-4">Featured Products</h2>
						<p className="text-lg text-elegant text-muted-foreground max-w-2xl mx-auto">
							Discover our carefully curated selection of quality lifestyle goods
						</p>
					</div>

					<div className="flex flex-col lg:flex-row gap-12">
						<CategoryFilter />
						<div className="flex-1">
							<ProductList products={products} />
							{products.length > 0 && (
								<div className="text-center mt-12">
									<Button asChild size="lg" className="rounded-lg shadow-card">
										<YnsLink href="/products">View All Products</YnsLink>
									</Button>
								</div>
							)}
						</div>
					</div>
				</div>
			</section>

			{/* Categories Grid */}
			<section className="w-full py-16">
				<div className="max-w-7xl mx-auto px-8 sm:px-16">
					<div className="mb-12 text-center">
						<h2 className="text-3xl text-display text-foreground mb-4">Shop by Category</h2>
						<p className="text-lg text-elegant text-muted-foreground max-w-2xl mx-auto">
							Browse our complete range of lifestyle products organized by category
						</p>
					</div>
					<div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
						{StoreConfig.categories.map(({ slug, image }) => (
							<CategoryBox key={slug} categorySlug={slug} src={image} />
						))}
					</div>
				</div>
			</section>
		</main>
	);
}
