import * as Commerce from "commerce-kit";
import type { MappedProduct } from "commerce-kit";

/**
 * Safely browse products, filtering out those without required metadata
 * This prevents Zod validation errors when products don't have slug metadata
 */
export async function safeProductBrowse(params: {
	first?: number;
	last?: number;
	offset?: number;
	filter?: {
		category?: string;
	};
}): Promise<MappedProduct[]> {
	try {
		// Get raw products from Stripe and filter them manually
		const stripe = Commerce.provider({
			secretKey: process.env.STRIPE_SECRET_KEY!,
			tagPrefix: undefined,
		});

		let rawProducts;
		
		if (params.filter?.category) {
			// Search for products by category
			rawProducts = await stripe.products.search({
				query: `active:'true' AND metadata['category']:'${params.filter.category}'`,
				limit: 100,
				expand: ["data.default_price"],
			});
		} else {
			// List all active products
			rawProducts = await stripe.products.list({
				limit: 100,
				active: true,
				expand: ["data.default_price"],
			});
		}

		// Filter products that have required slug metadata
		const validProducts = rawProducts.data.filter(p => 
			p.active && 
			!p.deleted && 
			p.default_price && 
			p.metadata?.slug // Only include products with slug metadata
		);

		// Apply pagination
		const startIndex = params.offset || 0;
		const endIndex = params.first ? startIndex + params.first : validProducts.length;
		const paginatedProducts = validProducts.slice(startIndex, endIndex);

		// Use commerce-kit's internal mapping function for valid products only
		if (paginatedProducts.length > 0) {
			const { mapProducts } = await import("commerce-kit/internal");
			
			// Create a response object that matches Stripe's structure
			const mockResponse = {
				...rawProducts,
				data: paginatedProducts
			};

			return mapProducts(mockResponse);
		}

		return [];
	} catch (error) {
		console.error('Error fetching products:', error);
		return [];
	}
}
