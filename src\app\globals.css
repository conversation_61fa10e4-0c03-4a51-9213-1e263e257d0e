@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme {
	--color-border: hsl(var(--border));
	--color-input: hsl(var(--input));
	--color-ring: hsl(var(--ring));
	--color-background: hsl(var(--background));
	--color-foreground: hsl(var(--foreground));

	--color-primary: hsl(var(--primary));
	--color-primary-foreground: hsl(var(--primary-foreground));

	--color-secondary: hsl(var(--secondary));
	--color-secondary-foreground: hsl(var(--secondary-foreground));

	--color-destructive: hsl(var(--destructive));
	--color-destructive-foreground: hsl(var(--destructive-foreground));

	--color-muted: hsl(var(--muted));
	--color-muted-foreground: hsl(var(--muted-foreground));

	--color-accent: hsl(var(--accent));
	--color-accent-foreground: hsl(var(--accent-foreground));

	--color-popover: hsl(var(--popover));
	--color-popover-foreground: hsl(var(--popover-foreground));

	--color-card: hsl(var(--card));
	--color-card-foreground: hsl(var(--card-foreground));

	--radius-lg: var(--radius);
	--radius-md: calc(var(--radius) - 2px);
	--radius-sm: calc(var(--radius) - 4px);

	--animate-accordion-down: accordion-down 0.2s ease-out;
	--animate-accordion-up: accordion-up 0.2s ease-out;

	--breakpoint-xs: 400px;
	--breakpoint-smb: 720px;

	@keyframes accordion-down {
		from {
			height: 0;
		}
		to {
			height: var(--radix-accordion-content-height);
		}
	}
	@keyframes accordion-up {
		from {
			height: var(--radix-accordion-content-height);
		}
		to {
			height: 0;
		}
	}
}

@utility container {
	margin-inline: auto;
	padding-inline: 2rem;
	@media (width >= --theme(--breakpoint-xs)) {
		max-width: none;
	}
	@media (width >= 1400px) {
		max-width: 1400px;
	}
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}
}

@utility text-pretty {
	text-wrap: balance;
	text-wrap: pretty;
}

/* Enhanced typography utilities for black and white design */
@utility text-elegant {
	font-weight: 400;
	letter-spacing: -0.025em;
	line-height: 1.5;
}

@utility text-heading {
	font-weight: 600;
	letter-spacing: -0.05em;
	line-height: 1.2;
}

@utility text-display {
	font-weight: 700;
	letter-spacing: -0.075em;
	line-height: 1.1;
}

/* Subtle shadow utilities for depth */
@utility shadow-elegant {
	box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

@utility shadow-card {
	box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

@utility border-elegant {
	border: 1px solid rgb(0 0 0 / 0.08);
}

@utility animation-fade-in {
	animation: fade-in 0.2s ease-out forwards;
}

@utility animation-slide-from-right {
	animation: slide-from-right 0.2s ease-out forwards;
}

@layer base {
	:root {
		/* Pure white background for clean look */
		--background: 0 0% 100%;
		--foreground: 0 0% 8%;

		/* Clean white cards with subtle shadows */
		--card: 0 0% 100%;
		--card-foreground: 0 0% 8%;

		/* Popover styling */
		--popover: 0 0% 100%;
		--popover-foreground: 0 0% 8%;

		/* Black primary for strong contrast */
		--primary: 0 0% 8%;
		--primary-foreground: 0 0% 98%;

		/* Light gray secondary */
		--secondary: 0 0% 96%;
		--secondary-foreground: 0 0% 8%;

		/* Muted grays for subtle elements */
		--muted: 0 0% 96%;
		--muted-foreground: 0 0% 45%;

		/* Accent using subtle gray */
		--accent: 0 0% 94%;
		--accent-foreground: 0 0% 8%;

		/* Keep destructive red for errors */
		--destructive: 0 84% 60%;
		--destructive-foreground: 0 0% 98%;

		/* Light gray borders */
		--border: 0 0% 90%;
		--input: 0 0% 90%;
		--ring: 0 0% 8%;

		--radius: 0.375rem;
	}

	.dark {
		/* Dark theme with inverted colors */
		--background: 0 0% 8%;
		--foreground: 0 0% 98%;

		--card: 0 0% 10%;
		--card-foreground: 0 0% 98%;

		--popover: 0 0% 10%;
		--popover-foreground: 0 0% 98%;

		--primary: 0 0% 98%;
		--primary-foreground: 0 0% 8%;

		--secondary: 0 0% 14%;
		--secondary-foreground: 0 0% 98%;

		--muted: 0 0% 14%;
		--muted-foreground: 0 0% 65%;

		--accent: 0 0% 16%;
		--accent-foreground: 0 0% 98%;

		--destructive: 0 62% 50%;
		--destructive-foreground: 0 0% 98%;

		--border: 0 0% 20%;
		--input: 0 0% 20%;
		--ring: 0 0% 83%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}

label:has([required]) > span:first-child {
	&::after {
		content: "*";
		@apply ml-1 text-destructive;
	}
}

.CollapsibleContent {
	overflow: hidden;
}
.CollapsibleContent[data-state="open"] {
	animation: slideDown 250ms ease-in-out;
}
.CollapsibleContent[data-state="closed"] {
	animation: slideUp 250ms ease-in-out;
}

input[type="search" i]::-webkit-search-cancel-button {
	display: none;
}

@keyframes slideDown {
	from {
		height: 0;
	}
	to {
		height: var(--radix-collapsible-content-height);
	}
}

@keyframes slideUp {
	from {
		height: var(--radix-collapsible-content-height);
	}
	to {
		height: 0;
	}
}

@keyframes fade-in {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slide-from-right {
	from {
		transform: translateX(100%);
	}
	to {
		transform: translateX(0);
	}
}

.nav-border-reveal::after {
	@apply content-[''] w-full h-px absolute bottom-0 bg-neutral-200;

	animation: nav-border-reveal;
	animation-timeline: scroll();
	animation-range: 0 100px;
}

@keyframes nav-border-reveal {
	0% {
		opacity: 0%;
	}
	100% {
		opacity: 100%;
	}
}
