{"Global.metadata.title": "Your Next Store - デモ", "Global.metadata.description": "オンラインストアを瞬時に構築するためのNext.jsボイラープレート：シンプル、迅速、強力。", "Global.notFound.title": "見つかりません", "Global.notFound.description": "お探しのページは存在しません。", "Global.notFound.goBackLink": "ホームに戻る", "Global.globalError.title": "問題が発生しました！", "Global.globalError.moreDetails": "詳細", "Global.globalError.tryAgainButton": "再試行", "Global.error.title": "おっと、問題が発生しました！", "Global.error.goBackLink": "再試行", "Global.footer.newsletterTitle": "ニュースレターを購読する", "Global.footer.categoriesTitle": "製品", "Global.newsletter.emailPlaceholder": "メールアドレスを入力", "Global.newsletter.subscribeButton": "購読", "Global.newsletter.success": "ニュースレターの購読に成功しました。", "Global.newsletter.error": "ニュースレターの購読に失敗しました。", "Global.nav.search.title": "検索", "Global.nav.search.placeholder": "製品を検索…", "Global.nav.cartSummary.itemsInCart": "カートのアイテム", "Global.nav.cartSummary.total": "合計", "Global.nav.cartSummary.totalItems": "{count, plural, =0 {アイテムなし} =1 {# アイテム} other {# アイテム}} カート内", "Global.addToCart.success": "{productName}をカートに追加しました", "Global.addToCart.actionButton": "カートに追加", "Global.addToCart.disabled": "在庫切れ", "Global.deliveryEstimates.atLeast": "少なくとも", "Global.deliveryEstimates.upTo": "最大", "Global.deliveryEstimates.businessDay": "{count, plural, =1 {# 営業日} other {# 営業日}}", "Global.deliveryEstimates.day": "{count, plural, =1 {# 日} other {# 日}}", "Global.deliveryEstimates.hour": "{count, plural, =1 {# 時間} other {# 時間}}", "Global.deliveryEstimates.month": "{count, plural, =1 {# ヶ月} other {# ヶ月}}", "Global.deliveryEstimates.week": "{count, plural, =1 {# 週間} other {# 週間}}", "Global.actions.shopNow": "今すぐ購入", "/.hero.title": "厳選されたコレクションを発見", "/.hero.description": "家庭やライフスタイルのために慎重に選ばれた製品を探る。", "/.hero.action": "今すぐ購入", "/.hero.link": "/category/accessories", "/search.notFound.title": "\"{query}\"の結果は見つかりませんでした", "/search.notFound.description": "申し訳ありませんが、検索クエリに一致する結果が見つかりませんでした。検索条件を変更してみてください", "/search.metadata.title": "検索: {query} · Your Next Store", "/search.page.title": "\"{query}\"を検索中", "/search.loading.title": "検索中", "/products.metadata.title": "すべての製品 · Your Next Store", "/products.page.title": "すべての製品", "/product.metadata.title": "{productName} · Your Next Store", "/product.page.allProducts": "すべての製品", "/product.page.imagesTitle": "画像", "/product.page.descriptionTitle": "説明", "/product.page.variantTitle": "バリアント", "/category.metadata.title": "{categoryName}カテゴリー · Your Next Store", "/category.page.title": "カテゴリー: {categoryName}", "/cart.modal.title": "ショッピングカート", "/cart.modal.openFullView": "（全体を見る）", "/cart.modal.quantity": "数量: {quantity}", "/cart.modal.total": "合計", "/cart.modal.shippingAndTaxesInfo": "送料と税金は次のステップで追加されます", "/cart.modal.goToPaymentButton": "支払いに進む", "/cart.metadata.title": "ショッピングカート · Your Next Store", "/cart.page.title": "あなたのカート", "/cart.page.checkoutTitle": "チェックアウト", "/cart.page.checkoutDescription": "以下に請求先と配送先の詳細を入力してください。", "/cart.page.summaryTable.imageCol": "画像", "/cart.page.summaryTable.productCol": "製品", "/cart.page.summaryTable.priceCol": "価格", "/cart.page.summaryTable.quantityCol": "数量", "/cart.page.summaryTable.totalCol": "合計", "/cart.page.summaryTable.totalSummary": "合計", "/cart.page.stripePayment.fillRequiredFields": "必須項目を入力してください。", "/cart.page.stripePayment.unexpectedError": "予期しないエラーが発生しました。", "/cart.page.stripePayment.billingSameAsShipping": "請求先住所は配送先住所と同じ", "/cart.page.stripePayment.billingAddressTitle": "請求先住所", "/cart.page.stripePayment.payNowButton": "今すぐ支払う", "/cart.page.stripePayment.errorTitle": "エラー", "/cart.page.stripePayment.fullName": "氏名", "/cart.page.stripePayment.address1": "住所", "/cart.page.stripePayment.address2": "住所（続き）", "/cart.page.stripePayment.postalCode": "郵便番号", "/cart.page.stripePayment.city": "市区町村", "/cart.page.stripePayment.state": "都道府県", "/cart.page.stripePayment.country": "国", "/cart.page.stripePayment.phone": "電話番号", "/cart.page.stripePayment.taxId": "税番号", "/cart.page.stripePayment.taxIdPlaceholder": "例：T1234567890123", "/cart.page.formErrors.nameRequired": "名前は必須です", "/cart.page.formErrors.cityRequired": "市区町村は必須です", "/cart.page.formErrors.countryRequired": "国は必須です", "/cart.page.formErrors.line1Required": "住所は必須です", "/cart.page.formErrors.postalCodeRequired": "郵便番号は必須です", "/cart.empty.title": "カートは空です", "/cart.empty.description": "カートに商品がまだ追加されていないようです。", "/cart.empty.continueShoppingButton": "ショッピングを続ける", "/order.metadata.title": "注文確認 · Your Next Store", "/order.page.title": "注文確認", "/order.page.description": "ありがとうございます！以下に注文の詳細が表示されます。", "/order.page.orderNumberTitle": "注文番号", "/order.page.productsTitle": "この注文の製品", "/order.page.price": "価格", "/order.page.quantity": "数量", "/order.page.total": "合計", "/order.page.detailsTitle": "注文詳細", "/order.page.shippingAddress": "配送先住所", "/order.page.billingAddress": "請求先住所", "/order.page.taxId": "税番号", "/order.page.paymentMethod": "支払い方法", "/order.page.cardBrand": "カード会社", "/order.page.last4CardDigitsLabel": "カード番号（末尾4桁）", "/order.paymentStatus.canceled": "キャンセル済み", "/order.paymentStatus.processing": "処理中", "/order.paymentStatus.requires_action": "アクションが必要です", "/order.paymentStatus.requires_capture": "キャプチャが必要です", "/order.paymentStatus.requires_confirmation": "確認が必要です", "/order.paymentStatus.requires_payment_method": "支払い方法が必要です", "/order.paymentStatus.succeeded": "成功しました"}