name: Test
on:
  pull_request:
  merge_group:
  push:
    branches:
      - main

concurrency:
  group: tests-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Enable Corepack
        run: corepack enable

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: package.json
          cache: "pnpm"

      - uses: actions/cache@v4
        with:
          path: |
            ${{ github.workspace }}/.next/cache
          key: ${{ runner.os }}-nextjs-${{ hashFiles('**/pnpm-lock.json') }}-${{ hashFiles('**.[jt]s', '**.[jt]sx') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('**/pnpm-lock.json') }}-

      - name: Install dependencies
        run: pnpm --version && pnpm install --frozen-lockfile

      - name: Check Biome
        run: pnpm biome check

      - name: Test
        run: pnpm test
