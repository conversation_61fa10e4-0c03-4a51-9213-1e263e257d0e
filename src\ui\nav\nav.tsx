import { CartSummaryNav } from "@/ui/nav/cart-summary-nav";
import { NavMenu } from "@/ui/nav/nav-menu";
import { SearchNav } from "@/ui/nav/search-nav";
import { SeoH1 } from "@/ui/seo-h1";
import { YnsLink } from "@/ui/yns-link";
import { UserIcon } from "lucide-react";

export const Nav = async () => {
	return (
		<header className="z-50 py-6 sticky top-0 bg-white/95 backdrop-blur-md border-b border-elegant nav-border-reveal">
			<div className="mx-auto flex max-w-7xl items-center gap-4 px-4 flex-row sm:px-6 lg:px-8">
				<YnsLink href="/" className="transition-opacity hover:opacity-70">
					<SeoH1 className="-mt-0.5 whitespace-nowrap text-xl font-bold">
						eClair Marketplace
					</SeoH1>
				</YnsLink>

				<div className="max-w-full flex shrink w-auto sm:mr-auto overflow-auto max-sm:order-2">
					<NavMenu />
				</div>
				<div className="mr-4 ml-auto sm:ml-0">
					<SearchNav />
				</div>
				<CartSummaryNav />
				<YnsLink
					href="/login"
					className="p-2 rounded-md transition-colors hover:bg-accent"
					aria-label="User account"
				>
					<UserIcon className="w-5 h-5 text-foreground" />
				</YnsLink>
			</div>
		</header>
	);
};
