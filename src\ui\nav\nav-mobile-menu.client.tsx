"use client";

import {
	Drawer,
	DrawerContent,
	<PERSON>er<PERSON><PERSON><PERSON>,
	DrawerHeader,
	Drawer<PERSON><PERSON><PERSON>,
	DrawerTrigger,
} from "@/ui/shadcn/drawer";
import { MenuIcon } from "lucide-react";
import { type ReactNode, useState } from "react";

export const NavMobileMenu = ({ children }: { children: ReactNode }) => {
	const [isOpen, setIsOpen] = useState(false);
	return (
		<Drawer open={isOpen} onOpenChange={setIsOpen}>
			<DrawerTrigger>
				<MenuIcon />
			</DrawerTrigger>
			<DrawerContent>
				<DrawerHeader>
					<DrawerTitle className="text-center">Menu</DrawerTitle>
					<DrawerDescription className="sr-only">Navigation menu</DrawerDescription>
				</DrawerHeader>
				<div
					onClick={(e) => {
						if (e.target instanceof HTMLElement && e.target.closest("a")) {
							setIsOpen(false);
						}
					}}
				>
					{children}
				</div>
			</DrawerContent>
		</Drawer>
	);
};
