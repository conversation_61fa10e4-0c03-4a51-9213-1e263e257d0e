name: Release
on: workflow_dispatch

permissions:
  contents: read

jobs:
  relase:
    name: Release
    runs-on: ubuntu-latest
    permissions:
      contents: write # to be able to publish a GitHub release
      issues: write # to be able to comment on released issues
      pull-requests: write # to be able to comment on released pull requests
      id-token: write # to enable use of OIDC for npm provenance
    env:
      NEXT_PUBLIC_URL: ${{ vars.NEXT_PUBLIC_URL }}
      STRIPE_CURRENCY: ${{ vars.STRIPE_CURRENCY }}
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_PUBLISHABLE_KEY }}
      STRIPE_SECRET_KEY: ${{ secrets.STRIPE_SECRET_KEY }}
      STRIPE_WEBHOOK_SECRET: ${{ secrets.STRIPE_WEBHOOK_SECRET }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: pnpm/action-setup@v3

      - uses: actions/setup-node@v4
        with:
          node-version-file: "package.json"
          cache: "pnpm"

      - uses: actions/cache@v4
        with:
          path: |
            ${{ github.workspace }}/.next/cache
          key: ${{ runner.os }}-nextjs-${{ hashFiles('**/pnpm-lock.json') }}-${{ hashFiles('**.[jt]s', '**.[jt]sx') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('**/pnpm-lock.json') }}-

      - name: Install dependencies
        run: pnpm --version && pnpm install --frozen-lockfile

      - name: Check Biome
        run: pnpm biome check

      - name: Build
        run: pnpm build

      - name: Test
        run: pnpm test

      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: pnpm semantic-release
