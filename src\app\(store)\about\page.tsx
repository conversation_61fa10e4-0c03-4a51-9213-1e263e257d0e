import { publicUrl } from "@/env.mjs";
import Image from "next/image";
import type { Metadata } from "next/types";

export const generateMetadata = async (): Promise<Metadata> => {
	return {
		title: "About Us - eClair Marketplace",
		description:
			"Learn about eClair Marketplace, your trusted home-based business for lifestyle goods including household items, clothing, and accessories.",
		alternates: { canonical: `${publicUrl}/about` },
	};
};

export default async function AboutPage() {
	return (
		<main className="pb-8">
			<div className="mx-auto max-w-4xl px-4 py-8">
				{/* Hero Section */}
				<div className="text-center mb-12">
					<h1 className="text-4xl font-bold tracking-tight text-black mb-4">About eClair Marketplace</h1>
					<p className="text-xl text-gray-700 max-w-2xl mx-auto">
						Your trusted home-based business for quality lifestyle goods
					</p>
				</div>

				{/* Main Content */}
				<div className="grid gap-12 md:grid-cols-2 items-center mb-12">
					<div>
						<h2 className="text-2xl font-semibold mb-4 text-black">Our Story</h2>
						<p className="text-muted-foreground mb-4">
							eClair Marketplace is a home-based e-commerce business dedicated to bringing you the finest
							lifestyle goods. We specialize in carefully curated products that enhance your daily life, from
							household essentials to fashionable clothing and accessories.
						</p>
						<p className="text-muted-foreground mb-4">
							Our platform allows customers to browse, order, and pay online with ease, ensuring a seamless
							shopping experience from the comfort of your home.
						</p>
						<p className="text-muted-foreground">
							We believe in quality, convenience, and customer satisfaction. Every product in our collection
							is selected with care to meet the diverse needs of modern families and individuals.
						</p>
					</div>
					<div className="relative h-64 md:h-80 rounded-lg overflow-hidden">
						<Image
							src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
							alt="eClair Marketplace lifestyle products"
							fill
							className="object-cover"
							sizes="(max-width: 768px) 100vw, 50vw"
						/>
					</div>
				</div>

				{/* Product Categories */}
				<div className="mb-12">
					<h2 className="text-2xl font-semibold mb-6 text-center text-black">What We Offer</h2>
					<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
						{[
							{
								title: "Household & Personal Care",
								description: "Essential items for your home and personal wellness",
								icon: "🏠",
							},
							{
								title: "Kitchenware",
								description: "Quality tools and accessories for your culinary adventures",
								icon: "🍳",
							},
							{
								title: "Women's Clothing",
								description: "Stylish and comfortable clothing for every occasion",
								icon: "👗",
							},
							{
								title: "Women's Accessories",
								description: "Beautiful accessories to complete your look",
								icon: "👜",
							},
							{
								title: "Men's Clothing",
								description: "Quality apparel for the modern gentleman",
								icon: "👔",
							},
							{
								title: "Children's Clothing",
								description: "Comfortable and durable clothing for kids",
								icon: "👶",
							},
							{
								title: "Swimwear",
								description: "Stylish swimwear for all ages and occasions",
								icon: "🏊",
							},
						].map((category, index) => (
							<div key={index} className="bg-card p-6 rounded-lg border">
								<div className="text-3xl mb-3">{category.icon}</div>
								<h3 className="font-semibold mb-2 text-black">{category.title}</h3>
								<p className="text-sm text-muted-foreground">{category.description}</p>
							</div>
						))}
					</div>
				</div>

				{/* Mission Statement */}
				<div className="bg-muted p-8 rounded-lg text-center">
					<h2 className="text-2xl font-semibold mb-4 text-black">Our Mission</h2>
					<p className="text-lg text-muted-foreground max-w-3xl mx-auto">
						To provide our customers with high-quality lifestyle products that enhance their daily lives,
						delivered with exceptional service and convenience through our user-friendly online platform.
					</p>
				</div>
			</div>
		</main>
	);
}
