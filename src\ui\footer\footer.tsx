import { getTranslations } from "@/i18n/server";
import StoreConfig from "@/store.config";
import { Newsletter } from "@/ui/footer/newsletter.client";
import { YnsLink } from "@/ui/yns-link";
import type { SVGAttributes } from "react";

const sections = [
	{
		header: "Shop",
		links: [
			{
				label: "All Products",
				href: "/products",
			},
			...StoreConfig.categories.slice(0, 4).map(({ name, slug }) => ({
				label: name,
				href: `/category/${slug}`,
			})),
		],
	},
	{
		header: "Company",
		links: [
			{
				label: "About Us",
				href: "/about",
			},
			{
				label: "Contact",
				href: "/contact",
			},
			{
				label: "Privacy Policy",
				href: "/privacy",
			},
			{
				label: "Terms of Service",
				href: "/terms",
			},
		],
	},
];

export async function Footer() {
	const t = await getTranslations("Global.footer");

	return (
		<footer className="w-full bg-background border-t border-elegant py-12 md:py-16">
			<div className="container flex max-w-7xl flex-row flex-wrap justify-center gap-16 text-sm sm:justify-between">
				<div className="">
					<div className="flex w-full max-w-sm flex-col gap-4">
						<h3 className="text-lg text-heading text-foreground">{t("newsletterTitle")}</h3>
						<Newsletter />
					</div>
				</div>

				<nav className="grid grid-cols-2 gap-16">
					{sections.map((section) => (
						<section key={section.header}>
							<h3 className="mb-4 text-base text-heading text-foreground">{section.header}</h3>
							<ul role="list" className="grid gap-3">
								{section.links.map((link) => (
									<li key={link.label}>
										<YnsLink
											className="text-elegant text-muted-foreground underline-offset-4 hover:underline hover:text-foreground transition-colors"
											href={link.href}
										>
											{link.label}
										</YnsLink>
									</li>
								))}
							</ul>
						</section>
					))}
				</nav>
			</div>
			<div className="container mt-12 flex max-w-7xl flex-col items-center justify-between gap-6 text-sm md:flex-row border-t border-elegant pt-8">
				<div className="text-center md:text-left">
					<p className="text-elegant text-foreground font-medium">© 2024 eClair Marketplace</p>
					<p className="text-elegant text-muted-foreground mt-1">Quality lifestyle goods for modern living</p>
				</div>
				<div className="flex items-center gap-6">
					<YnsLink
						className="inline-flex items-center gap-2 text-elegant text-muted-foreground transition-colors hover:text-foreground"
						href="/contact"
					>
						<span className="text-base">📍</span>
						Surrey, BC, Canada
						<span className="sr-only">Location</span>
					</YnsLink>
					<YnsLink
						className="inline-flex items-center gap-2 text-elegant text-muted-foreground transition-colors hover:text-foreground"
						href="mailto:<EMAIL>"
					>
						<span className="text-base">✉️</span>
						Contact Us
						<span className="sr-only">Email</span>
					</YnsLink>
					<YnsLink
						className="inline-flex items-center gap-2 text-elegant text-muted-foreground transition-colors hover:text-foreground"
						href={StoreConfig.social.x}
						target="_blank"
						rel="noopener noreferrer"
					>
						<TwitterIcon className="h-5 w-5" />
						<span className="sr-only">Twitter/X</span>
					</YnsLink>
				</div>
			</div>
		</footer>
	);
}

function TwitterIcon(props: SVGAttributes<SVGSVGElement>) {
	return (
		<svg {...props} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 596 596" fill="none">
			<path
				fill="#fff"
				d="m1 19 230 307L0 577h52l203-219 164 219h177L353 252 568 19h-52L329 221 179 19H1Zm77 38h82l359 481h-81L78 57Z"
			/>
		</svg>
	);
}
