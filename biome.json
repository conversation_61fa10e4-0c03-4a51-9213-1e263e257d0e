{"$schema": "https://biomejs.dev/schemas/1.9.2/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": []}, "formatter": {"enabled": true, "useEditorconfig": true, "formatWithErrors": false, "indentStyle": "tab", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 110, "attributePosition": "auto", "bracketSpacing": true, "ignore": ["**/.next", "**/node_modules", "**/pnpm-lock.yaml"]}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": false, "complexity": {"noBannedTypes": "error", "noUselessThisAlias": "error", "noUselessTypeConstraint": "error", "useLiteralKeys": "error", "useOptionalChain": "error"}, "correctness": {"noEmptyPattern": "off", "noPrecisionLoss": "error", "noUnusedVariables": "off", "useArrayLiterals": "off"}, "style": {"noDefaultExport": "off", "noInferrableTypes": "error", "noNamespace": "error", "useAsConstAssertion": "error", "useBlockStatements": "off", "useConsistentArrayType": "off", "useForOf": "error", "useImportType": "error", "useShorthandFunctionType": "error"}, "suspicious": {"noEmptyBlockStatements": "off", "noEmptyInterface": "off", "noExplicitAny": "error", "noExtraNonNullAssertion": "error", "noMisleadingInstantiator": "error", "noUnsafeDeclarationMerging": "error", "useAwait": "off", "useNamespaceKeyword": "error"}}, "ignore": ["**/*.js", "**/*.jsx", "**/*.mjs", "src/script/**/*.ts"]}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "always", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "double", "attributePosition": "auto", "bracketSpacing": true}}, "overrides": [{"include": ["src/app/**/{page,layout,loading,route,not-found,error,global-error,default,robots,sitemap,opengraph-image}.ts?(x)", "src/i18n.ts", "*.d.ts", "tailwind.config.ts", "prettier.config.js", "middleware.ts", "commitlint.config.ts", "vitest.config.ts"], "linter": {"rules": {"style": {"noDefaultExport": "off"}}}}]}