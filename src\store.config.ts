export const storeConfig = {
	categories: [
		{
			name: "Household & Personal Care",
			slug: "household-personal-care",
			image:
				"https://images.unsplash.com/photo-1584622650111-993a426fbf0a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
		},
		{
			name: "Kitchenware",
			slug: "kitchenware",
			image:
				"https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
		},
		{
			name: "Women's Clothing",
			slug: "womens-clothing",
			image:
				"https://images.unsplash.com/photo-1445205170230-053b83016050?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
		},
		{
			name: "Women's Accessories",
			slug: "womens-accessories",
			image:
				"https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
		},
		{
			name: "Men's Clothing",
			slug: "mens-clothing",
			image:
				"https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
		},
		{
			name: "Children's Clothing",
			slug: "childrens-clothing",
			image:
				"https://images.unsplash.com/photo-1519238263530-99bdd11df2ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
		},
		{
			name: "Swimwear",
			slug: "swimwear",
			image:
				"https://plus.unsplash.com/premium_photo-1664392083992-8d324e524e1c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
		},
	],

	social: {
		x: "#",
		facebook: "#",
	},

	contact: {
		email: "<EMAIL>",
		phone: "+****************",
		address: "19292 72a Ave Surrey, BC V4N 5Y3 Canada",
	},
};

export type StoreConfig = typeof storeConfig;
export default storeConfig;
