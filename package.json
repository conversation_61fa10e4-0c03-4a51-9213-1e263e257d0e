{"$schema": "https://json.schemastore.org/package", "name": "eclairmarketplace", "version": "1.12.0", "license": "AGPL-3.0-only", "private": true, "type": "module", "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "biome check --write", "test": "vitest", "prepare": "pnpm husky", "docker:build": "docker build -t yournextstore .", "docker:run": "docker run -d -p 3000:3000 yournextstore"}, "dependencies": {"@ai-sdk/openai": "1.3.22", "@next/mdx": "15.4.0-canary.53", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-collapsible": "1.1.11", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-icons": "1.3.2", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-navigation-menu": "1.2.13", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-toggle": "1.1.9", "@radix-ui/react-toggle-group": "1.1.10", "@radix-ui/react-tooltip": "1.2.7", "@splinetool/react-spline": "4.0.0", "@splinetool/runtime": "1.9.96", "@stripe/react-stripe-js": "3.7.0", "@stripe/stripe-js": "7.3.0", "@t3-oss/env-nextjs": "0.13.6", "@vercel/analytics": "1.5.0", "@vercel/blob": "1.1.1", "@vercel/edge-config": "1.4.0", "@vercel/speed-insights": "1.2.0", "ai": "4.3.16", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "commerce-kit": "0.0.40", "intl-messageformat": "10.7.16", "jose": "6.0.11", "lucide-react": "0.511.0", "ms": "^2.1.3", "nanoid": "5.1.5", "next": "15.4.0-canary.53", "next-mdx-remote": "5.0.0", "next-themes": "0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "schema-dts": "1.1.5", "server-only": "0.0.1", "sonner": "2.0.3", "stripe": "18.1.1", "tailwind-merge": "3.3.0", "tailwindcss-animate": "1.0.7", "trieve-ts-sdk": "0.0.115", "vaul": "1.1.2", "zod": "3.25.30"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@commitlint/cli": "19.8.1", "@commitlint/config-conventional": "19.8.1", "@commitlint/types": "19.8.1", "@next/env": "15.4.0-canary.53", "@semantic-release/changelog": "6.0.3", "@semantic-release/commit-analyzer": "13.0.1", "@semantic-release/git": "10.0.1", "@semantic-release/github": "11.0.3", "@semantic-release/npm": "12.0.1", "@semantic-release/release-notes-generator": "14.0.3", "@tailwindcss/forms": "0.5.10", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/typography": "0.5.16", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@types/mdx": "2.0.13", "@types/node": "^22.15.21", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "@vitejs/plugin-react": "4.5.0", "babel-plugin-react-compiler": "19.1.0-rc.2", "husky": "9.1.7", "lint-staged": "16.0.0", "mdx": "0.3.1", "postcss": "8.5.3", "semantic-release": "24.2.5", "sharp": "0.34.2", "tailwindcss": "^4.1.7", "tsx": "4.19.4", "typescript": "5.8.3", "vite-tsconfig-paths": "5.1.4", "vitest": "3.1.4"}, "engines": {"node": "^20.11.1 || ^22.0.0"}, "release": {"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git", "@semantic-release/github"]}, "lint-staged": {"*": ["pnpm biome check --write --staged --no-errors-on-unmatched --files-ignore-unknown=true"]}, "pnpm": {"ignoredBuiltDependencies": ["@vercel/speed-insights"], "onlyBuiltDependencies": ["@vercel/speed-insights"]}}