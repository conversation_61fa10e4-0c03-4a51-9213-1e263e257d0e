import { publicUrl } from "@/env.mjs";
import { safeProductBrowse } from "@/lib/commerce-utils";
import { deslugify } from "@/lib/utils";
import { CategoryFilter } from "@/ui/category-filter";
import { ProductList } from "@/ui/products/product-list";
import type { Metadata } from "next/types";

export const generateMetadata = async (props: {
	searchParams: Promise<{ category?: string }>;
}): Promise<Metadata> => {
	const searchParams = await props.searchParams;
	const category = searchParams.category;

	const title = category
		? `${deslugify(category)} - eClair Marketplace`
		: "All Products - eClair Marketplace";

	return {
		title,
		alternates: { canonical: `${publicUrl}/products${category ? `?category=${category}` : ""}` },
	};
};

export default async function AllProductsPage(props: {
	searchParams: Promise<{ category?: string }>;
}) {
	const searchParams = await props.searchParams;
	const category = searchParams.category;

	const products = await safeProductBrowse({
		first: 100,
		filter: category ? { category } : undefined,
	});

	const pageTitle = category ? deslugify(category) : "All Products";

	return (
		<main className="pb-8">
			<div className="mb-8">
				<h1 className="text-3xl font-bold leading-none tracking-tight text-black mb-2">{pageTitle}</h1>
				<p className="text-gray-700">
					{category
						? `Browse our collection of ${deslugify(category).toLowerCase()}`
						: "Discover our complete range of lifestyle products"}
				</p>
			</div>

			<div className="flex flex-col lg:flex-row gap-8">
				<CategoryFilter />
				<div className="flex-1">
					<ProductList products={products} />
				</div>
			</div>
		</main>
	);
}
