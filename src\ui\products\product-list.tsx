import { getLocale } from "@/i18n/server";
import { formatMoney } from "@/lib/utils";
import { JsonLd, mappedProductsToJsonLd } from "@/ui/json-ld";
import { YnsLink } from "@/ui/yns-link";
import type * as Commerce from "commerce-kit";
import Image from "next/image";

export const ProductList = async ({ products }: { products: Commerce.MappedProduct[] }) => {
	const locale = await getLocale();

	return (
		<>
			<ul className="mt-8 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
				{products.map((product, idx) => {
					return (
						<li key={product.id} className="group">
							<YnsLink href={`/product/${product.metadata.slug}`} className="block">
								<article className="overflow-hidden bg-white border border-elegant rounded-lg shadow-elegant hover:shadow-card transition-all duration-300 group-hover:-translate-y-1">
									{product.images[0] && (
										<div className="aspect-square w-full overflow-hidden bg-muted">
											<Image
												className="w-full h-full object-cover object-center transition-transform duration-500 group-hover:scale-105"
												src={product.images[0]}
												width={768}
												height={768}
												loading={idx < 3 ? "eager" : "lazy"}
												priority={idx < 3}
												sizes="(max-width: 1024x) 100vw, (max-width: 1280px) 50vw, 700px"
												alt={product.name}
											/>
										</div>
									)}
									<div className="p-6">
										<h2 className="text-lg text-heading text-foreground mb-2 group-hover:text-primary transition-colors">
											{product.name}
										</h2>
										<footer className="text-elegant text-muted-foreground">
											{product.default_price.unit_amount && (
												<p className="text-lg font-semibold text-foreground">
													{formatMoney({
														amount: product.default_price.unit_amount,
														currency: product.default_price.currency,
														locale,
													})}
												</p>
											)}
										</footer>
									</div>
								</article>
							</YnsLink>
						</li>
					);
				})}
			</ul>
			<JsonLd jsonLd={mappedProductsToJsonLd(products)} />
		</>
	);
};
