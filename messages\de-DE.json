{"Global.metadata.title": "Your Next Store - Demo", "Global.metadata.description": "Ein Next.js- Grundgerüst für den sofortigen Aufbau Ihres Online-Shops: e<PERSON><PERSON><PERSON>, schnell, leistungsstark.", "Global.notFound.title": "Nicht gefunden", "Global.notFound.description": "Die gesuchte Seite existiert nicht.", "Global.notFound.goBackLink": "Zur Startsei<PERSON>", "Global.globalError.title": "Etwas ist schiefgelaufen!", "Global.globalError.moreDetails": "<PERSON><PERSON><PERSON> Infos", "Global.globalError.tryAgainButton": "<PERSON><PERSON><PERSON> versuchen", "Global.error.title": "Ups, etwas ist schiefgelaufen!", "Global.error.goBackLink": "<PERSON><PERSON><PERSON> versuchen", "Global.footer.newsletterTitle": "Abonniere unseren Newsletter", "Global.footer.categoriesTitle": "Produkte", "Global.newsletter.emailPlaceholder": "Gib deine E-Mail-Adresse ein", "Global.newsletter.subscribeButton": "Abonnieren", "Global.newsletter.success": "Du hast den Newsletter abonniert.", "Global.newsletter.error": "Beim <PERSON>en des Newsletters ist ein Fehler aufgetreten.", "Global.nav.search.title": "<PERSON><PERSON>", "Global.nav.search.placeholder": "Suche nach Produkten...", "Global.nav.cartSummary.itemsInCart": "Produkte im Warenkorb", "Global.nav.cartSummary.total": "Gesamt", "Global.nav.cartSummary.totalItems": "{count, plural, =0 {<PERSON><PERSON> Produkte} =1 {# Produkt} other {# Produkte}} im Warenkorb", "Global.addToCart.success": "{productName} zum Warenkorb hinzugefügt", "Global.addToCart.actionButton": "In den Warenkorb", "Global.deliveryEstimates.atLeast": "Mindestens", "Global.deliveryEstimates.upTo": "<PERSON><PERSON> zu", "Global.deliveryEstimates.businessDay": "{count, plural, =1 {# Werktag} other {# Werktage}}", "Global.deliveryEstimates.day": "{count, plural, =1 {# Tag} other {# Tage}}", "Global.deliveryEstimates.hour": "{count, plural, =1 {# <PERSON>und<PERSON>} other {# Stund<PERSON>}}", "Global.deliveryEstimates.month": "{count, plural, =1 {# <PERSON>t} other {# Monate}}", "Global.deliveryEstimates.week": "{count, plural, =1 {# Woche} other {# Wochen}}", "Global.actions.shopNow": "Jetzt einkaufen", "/.hero.title": "Entdecke unsere kuratierte Kollektion", "/.hero.description": "Entdecke unsere sorgfältig ausgewählten Produkte für Dein Zuhause und Deinen Lifestyle.", "/.hero.action": "Jetzt einkaufen", "/.hero.link": "/category/accessories", "/search.notFound.title": "<PERSON><PERSON> für \"{query}\"", "/search.notFound.description": "Wir konnten keine passenden Produkte für Deine Suche finden. Überprüfe die Rechtschreibung oder verwende allgemeinere Begriffe.", "/search.metadata.title": "Suche: {query} · Your Next Store", "/search.page.title": "<PERSON><PERSON> nach \"{query}\"", "/search.loading.title": "<PERSON><PERSON> nach", "/products.metadata.title": "Alle Produkte · Your Next Store", "/products.page.title": "Alle Produkte", "/product.metadata.title": "{productName} · Your Next Store", "/product.page.allProducts": "Alle Produkte", "/product.page.imagesTitle": "Bilder", "/product.page.descriptionTitle": "Bescheibung", "/product.page.variantTitle": "<PERSON><PERSON><PERSON>", "/category.metadata.title": "{categoryName} Kategorie · Your Next Store", "/category.page.title": "Kategorie: {categoryName}", "/cart.modal.title": "<PERSON><PERSON><PERSON>", "/cart.modal.openFullView": "(Vollansicht öffnen)", "/cart.modal.quantity": "<PERSON><PERSON><PERSON>: {quantity}", "/cart.modal.total": "Gesamt", "/cart.modal.shippingAndTaxesInfo": "Die Versandkosten und Steuern werden im nächsten Schritt hinzugefügt.", "/cart.modal.goToPaymentButton": "<PERSON>ter zur Bezahlung", "/cart.metadata.title": "Warenkorb · Your Next Store", "/cart.page.title": "<PERSON><PERSON>", "/cart.page.checkoutTitle": "<PERSON><PERSON>", "/cart.page.checkoutDescription": "Geben Si<PERSON> unten die Rechnungs- und Versanddaten an.", "/cart.page.summaryTable.imageCol": "Bild", "/cart.page.summaryTable.productCol": "Produkt", "/cart.page.summaryTable.priceCol": "Pre<PERSON>", "/cart.page.summaryTable.quantityCol": "<PERSON><PERSON><PERSON>", "/cart.page.summaryTable.totalCol": "Gesamt", "/cart.page.summaryTable.totalSummary": "GESAMT", "/cart.page.stripePayment.fillRequiredFields": "Bitte fülle alle benötigten Felder aus.", "/cart.page.stripePayment.unexpectedError": "Ein unerwarteter Fehler ist aufgetreten.", "/cart.page.stripePayment.billingSameAsShipping": "Gleiche Re<PERSON>nungsadresse wie Lieferadresse", "/cart.page.stripePayment.billingAddressTitle": "Re<PERSON>nungsadress<PERSON>", "/cart.page.stripePayment.payNowButton": "Jetzt bezahlen", "/cart.page.stripePayment.errorTitle": "<PERSON><PERSON>", "/cart.page.stripePayment.fullName": "Vor- und Nachname", "/cart.page.stripePayment.address1": "Adresse 1", "/cart.page.stripePayment.address2": "Adresse 2 (optional)", "/cart.page.stripePayment.postalCode": "PLZ", "/cart.page.stripePayment.city": "<PERSON><PERSON><PERSON><PERSON>", "/cart.page.stripePayment.state": "Bundesland", "/cart.page.stripePayment.country": "Land", "/cart.page.stripePayment.phone": "Telefonnummer", "/cart.page.stripePayment.taxId": "Steuer-ID", "/cart.page.stripePayment.taxIdPlaceholder": "eg. DE123456789", "/cart.page.formErrors.nameRequired": "Der Name ist notwendig", "/cart.page.formErrors.cityRequired": "<PERSON> ist notwendig", "/cart.page.formErrors.countryRequired": "Das Land ist notwendig", "/cart.page.formErrors.line1Required": "Die Adresse ist notwendig", "/cart.page.formErrors.postalCodeRequired": "Die PLZ ist notwendig", "/cart.empty.title": "<PERSON><PERSON> ist leer", "/cart.empty.description": "<PERSON><PERSON> jetzt hast Du noch keine Produkte in den Warenkorb gelegt.", "/cart.empty.continueShoppingButton": "<PERSON><PERSON> e<PERSON>", "/order.metadata.title": "Bestellbestätigung · Your Next Store", "/order.page.title": "Bestellbestätigung", "/order.page.description": "Vielen Dank. Du findest die Bestelldetails unten.", "/order.page.orderNumberTitle": "Auftragsnummer", "/order.page.productsTitle": "Produkte in dieser Bestellung", "/order.page.price": "Pre<PERSON>", "/order.page.quantity": "<PERSON><PERSON>", "/order.page.total": "Gesamt", "/order.page.detailsTitle": "Bestelldetails", "/order.page.shippingAddress": "Lieferadresse", "/order.page.billingAddress": "Re<PERSON>nungsadress<PERSON>", "/order.page.taxId": "Steuer-ID", "/order.page.paymentMethod": "Zahlungsart", "/order.page.cardBrand": "Kreditkarte", "/order.page.last4CardDigitsLabel": "Kreditkarte endet mit", "/order.paymentStatus.canceled": "Abgebrochen", "/order.paymentStatus.processing": "Bearbeitung", "/order.paymentStatus.requires_action": "Erfordert eine Aktion", "/order.paymentStatus.requires_capture": "<PERSON><PERSON><PERSON><PERSON>", "/order.paymentStatus.requires_confirmation": "<PERSON><PERSON><PERSON><PERSON>ät<PERSON>", "/order.paymentStatus.requires_payment_method": "<PERSON><PERSON><PERSON><PERSON>", "/order.paymentStatus.succeeded": "Erfolgreich"}