"use client";

import { cn } from "@/lib/utils";
import StoreConfig from "@/store.config";
import { YnsLink } from "@/ui/yns-link";
import { useSearchParams } from "next/navigation";
import { Suspense } from "react";

function CategoryFilterContent() {
	const searchParams = useSearchParams();
	const selectedCategory = searchParams.get("category");

	return (
		<div className="w-full lg:w-64 mb-8 lg:mb-0">
			<div className="bg-card border rounded-lg p-6">
				<h2 className="text-lg font-semibold mb-4 text-black">Categories</h2>
				<div className="space-y-2">
					{/* All Products Link */}
					<YnsLink
						href="/products"
						className={cn(
							"block px-3 py-2 rounded-md text-sm transition-colors hover:bg-muted",
							!selectedCategory && "bg-primary text-primary-foreground hover:bg-primary/90",
						)}
					>
						All Products
					</YnsLink>

					{/* Category Links */}
					{StoreConfig.categories.map((category) => (
						<YnsLink
							key={category.slug}
							href={`/products?category=${category.slug}`}
							className={cn(
								"block px-3 py-2 rounded-md text-sm transition-colors hover:bg-muted",
								selectedCategory === category.slug &&
									"bg-primary text-primary-foreground hover:bg-primary/90",
							)}
						>
							{category.name}
						</YnsLink>
					))}
				</div>
			</div>

			{/* Featured Categories */}
			<div className="mt-6 bg-card border rounded-lg p-6">
				<h3 className="text-lg font-semibold mb-4 text-black">Featured</h3>
				<div className="space-y-3">
					{StoreConfig.categories.slice(0, 3).map((category) => (
						<YnsLink key={category.slug} href={`/category/${category.slug}`} className="block group">
							<div className="relative h-20 rounded-md overflow-hidden mb-2">
								<div
									className="w-full h-full bg-cover bg-center group-hover:scale-105 transition-transform duration-200"
									style={{
										backgroundImage: `url(${category.image})`,
									}}
								/>
								<div className="absolute inset-0 bg-gray-900/20 group-hover:bg-gray-900/30 transition-colors" />
								<div className="absolute inset-0 flex items-center justify-center">
									<span className="text-white text-xs font-medium text-center px-2">{category.name}</span>
								</div>
							</div>
						</YnsLink>
					))}
				</div>
			</div>
		</div>
	);
}

export function CategoryFilter() {
	return (
		<Suspense fallback={<div className="w-full lg:w-64 mb-8 lg:mb-0">Loading...</div>}>
			<CategoryFilterContent />
		</Suspense>
	);
}
