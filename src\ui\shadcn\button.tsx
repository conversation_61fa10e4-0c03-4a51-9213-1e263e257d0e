import { Slot } from "@radix-ui/react-slot";
import { type VariantProps, cva } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
	"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
	{
		variants: {
			variant: {
				default: "bg-primary text-primary-foreground shadow-elegant hover:bg-primary/90 hover:shadow-card active:scale-[0.98]",
				destructive: "bg-destructive text-destructive-foreground shadow-elegant hover:bg-destructive/90 hover:shadow-card active:scale-[0.98]",
				outline: "border border-elegant bg-background shadow-elegant hover:bg-accent hover:text-accent-foreground hover:shadow-card active:scale-[0.98]",
				secondary: "bg-secondary text-secondary-foreground shadow-elegant hover:bg-secondary/80 hover:shadow-card active:scale-[0.98]",
				ghost: "hover:bg-accent hover:text-accent-foreground active:scale-[0.98]",
				link: "text-primary underline-offset-4 hover:underline active:scale-[0.98]",
			},
			size: {
				default: "h-10 px-6 py-2 text-sm",
				sm: "h-8 rounded-md px-4 text-xs",
				lg: "h-12 rounded-lg px-8 text-base font-semibold",
				icon: "h-10 w-10",
			},
		},
		defaultVariants: {
			variant: "default",
			size: "default",
		},
	},
);

export interface ButtonProps
	extends React.ButtonHTMLAttributes<HTMLButtonElement>,
		VariantProps<typeof buttonVariants> {
	asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
	({ className, variant, size, asChild = false, ...props }, ref) => {
		const Comp = asChild ? Slot : "button";
		return <Comp className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props} />;
	},
);
Button.displayName = "Button";

export { Button, buttonVariants };
