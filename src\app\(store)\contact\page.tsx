import { publicUrl } from "@/env.mjs";
import type { Metadata } from "next/types";

export const generateMetadata = async (): Promise<Metadata> => {
	return {
		title: "Contact Us - eClair Marketplace",
		description:
			"Get in touch with eClair Marketplace. Find our location, contact information, and business hours.",
		alternates: { canonical: `${publicUrl}/contact` },
	};
};

export default async function ContactPage() {
	return (
		<main className="pb-8">
			<div className="mx-auto max-w-6xl px-4 py-8">
				{/* Hero Section */}
				<div className="text-center mb-12">
					<h1 className="text-4xl font-bold tracking-tight text-black mb-4">Contact Us</h1>
					<p className="text-xl text-gray-700 max-w-2xl mx-auto">
						We'd love to hear from you. Get in touch with any questions about our products or services.
					</p>
				</div>

				<div className="grid gap-12 lg:grid-cols-2">
					{/* Contact Information */}
					<div>
						<h2 className="text-2xl font-semibold mb-6 text-black">Get In Touch</h2>

						<div className="space-y-6">
							{/* Address */}
							<div className="flex items-start space-x-4">
								<div className="bg-blue-100 p-3 rounded-lg">
									<svg
										className="w-6 h-6 text-blue-600"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
										/>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
										/>
									</svg>
								</div>
								<div>
									<h3 className="font-semibold mb-1 text-black">Address</h3>
									<p className="text-muted-foreground">
										19292 72a Ave
										<br />
										Surrey, BC V4N 5Y3
										<br />
										Canada
									</p>
								</div>
							</div>

							{/* Email */}
							<div className="flex items-start space-x-4">
								<div className="bg-green-100 p-3 rounded-lg">
									<svg
										className="w-6 h-6 text-green-600"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
										/>
									</svg>
								</div>
								<div>
									<h3 className="font-semibold mb-1 text-black">Email</h3>
									<p className="text-muted-foreground">
										<a
											href="mailto:<EMAIL>"
											className="hover:text-primary transition-colors"
										>
											<EMAIL>
										</a>
									</p>
								</div>
							</div>

							{/* Phone */}
							<div className="flex items-start space-x-4">
								<div className="bg-purple-100 p-3 rounded-lg">
									<svg
										className="w-6 h-6 text-purple-600"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
										/>
									</svg>
								</div>
								<div>
									<h3 className="font-semibold mb-1 text-black">Phone</h3>
									<p className="text-muted-foreground">
										<a href="tel:+***********" className="hover:text-primary transition-colors">
											+****************
										</a>
									</p>
								</div>
							</div>
						</div>

						{/* Business Hours */}
						<div className="mt-8">
							<h3 className="text-xl font-semibold mb-4 text-black">Business Hours</h3>
							<div className="space-y-2 text-muted-foreground">
								<div className="flex justify-between">
									<span>Monday - Friday:</span>
									<span>9:00 AM - 6:00 PM</span>
								</div>
								<div className="flex justify-between">
									<span>Saturday:</span>
									<span>10:00 AM - 4:00 PM</span>
								</div>
								<div className="flex justify-between">
									<span>Sunday:</span>
									<span>Closed</span>
								</div>
							</div>
						</div>

						{/* About Our Service */}
						<div className="mt-8 p-6 bg-muted rounded-lg">
							<h3 className="text-lg font-semibold mb-3 text-black">About Our Service</h3>
							<p className="text-muted-foreground">
								eClair Marketplace is a home-based e-commerce business serving customers across Canada. We
								offer online ordering and secure payment processing for all our lifestyle products. Orders are
								processed Monday through Friday, with weekend support available for urgent inquiries.
							</p>
						</div>
					</div>

					{/* Map */}
					<div>
						<h2 className="text-2xl font-semibold mb-6 text-black">Find Us</h2>
						<div className="bg-muted rounded-lg overflow-hidden" style={{ height: "500px" }}>
							<iframe
								src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2607.2356789012!2d-122.6912345678!3d49.1334567890123!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2s19292+72A+Ave%2C+Surrey%2C+BC!5e0!3m2!1sen!2sca!4v1234567890123!5m2!1sen!2sca"
								width="100%"
								height="100%"
								style={{ border: 0 }}
								allowFullScreen
								loading="lazy"
								referrerPolicy="no-referrer-when-downgrade"
								title="eClair Marketplace Location"
							></iframe>
						</div>
						<p className="text-sm text-muted-foreground mt-4 text-center">
							Located in Langley Township, British Columbia
						</p>
					</div>
				</div>
			</div>
		</main>
	);
}
