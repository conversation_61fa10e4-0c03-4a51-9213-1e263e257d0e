import { getTranslations } from "@/i18n/server";
import { deslugify } from "@/lib/utils";
import { YnsLink } from "@/ui/yns-link";
import Image, { type ImageProps } from "next/image";

export async function CategoryBox({
	categorySlug,
	src,
}: {
	categorySlug: string;
	src: ImageProps["src"];
}) {
	const t = await getTranslations("Global.actions");

	return (
		<YnsLink href={`/category/${categorySlug}`} className="group relative block">
			<div className="relative overflow-hidden rounded-lg aspect-[4/3] border border-elegant shadow-elegant group-hover:shadow-card transition-all duration-300">
				{typeof src === 'string' ? (
					<Image
						alt={`${deslugify(categorySlug)} category`}
						className="object-cover transition-transform duration-500 group-hover:scale-105"
						sizes="(max-width: 1024px) 100vw, (max-width: 1280px) 50vw, 620px"
						src={src}
						fill
					/>
				) : (
					<Image
						alt={`${deslugify(categorySlug)} category`}
						className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
						sizes="(max-width: 1024px) 100vw, (max-width: 1280px) 50vw, 620px"
						src={src}
					/>
				)}
				<div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
			</div>
			<div className="mt-4 space-y-2">
				<h3 className="text-xl text-heading text-foreground group-hover:text-primary transition-colors">
					{deslugify(categorySlug)}
				</h3>
				<p className="text-elegant text-muted-foreground group-hover:text-foreground transition-colors">
					{t("shopNow")}
				</p>
			</div>
		</YnsLink>
	);
}
